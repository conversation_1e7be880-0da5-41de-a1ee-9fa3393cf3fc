@plugin '@tailwindcss/typography';

@layer base {
  .prose {
    @apply prose-headings:!mb-3 prose-headings:!text-foreground prose-h3:italic prose-p:!text-foreground prose-a:!text-foreground prose-a:!decoration-dashed prose-a:underline-offset-8 hover:prose-a:text-accent prose-blockquote:!border-l-accent/50 prose-blockquote:opacity-80 prose-figcaption:!text-foreground prose-figcaption:opacity-70 prose-strong:!text-foreground prose-code:rounded prose-code:bg-muted/75 prose-code:p-1 prose-code:!text-foreground prose-code:before:!content-none prose-code:after:!content-none prose-ol:!text-foreground prose-ul:overflow-x-clip prose-ul:!text-foreground prose-li:marker:!text-accent prose-table:text-foreground prose-th:border prose-th:border-border prose-td:border prose-td:border-border prose-img:mx-auto prose-img:!my-2 prose-img:border-2 prose-img:border-border prose-hr:!border-border;
  }
  .prose a {
    @apply break-words hover:!text-accent;
  }
  .prose thead th:first-child,
  tbody td:first-child,
  tfoot td:first-child {
    padding-inline-start: 0.5714286em !important;
  }
  .prose h2#table-of-contents {
    @apply mb-2;
  }
  .prose details {
    @apply inline-block cursor-pointer text-foreground select-none;
  }
  .prose summary {
    @apply focus-visible:no-underline focus-visible:outline-2 focus-visible:outline-offset-1 focus-visible:outline-accent focus-visible:outline-dashed;
  }
  .prose h2#table-of-contents + p {
    @apply hidden;
  }

  /* ===== Code Blocks & Syntax Highlighting ===== */
  .prose pre:has(code) {
    @apply border border-border;
    font-family: 'Maple-Mono', 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace !important;
    font-size: 16px !important;
  }

  .prose code,
  .prose blockquote {
    @apply break-words;
  }

  .prose code {
    font-family: 'Maple-Mono', 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace !important;
    font-size: 16px !important;
  }

  .prose pre code {
    font-family: 'Maple-Mono', 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace !important;
    font-size: 16px !important;
  }

  .prose table code {
    /* add line breaks whenever necessary for codes under table */
    @apply break-all sm:break-normal;
    font-family: 'Maple-Mono', 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace !important;
    font-size: 16px !important;
  }

  pre > code {
    white-space: pre;
    font-family: 'Maple-Mono', 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace !important;
    font-size: 16px !important;
  }

  /* Apply Dark Theme (if multi-theme specified) */
  html[data-theme="dark"] pre:has(code),
  html[data-theme="dark"] pre:has(code) span {
    color: var(--shiki-dark) !important;
    background-color: var(--shiki-dark-bg) !important;
    font-style: var(--shiki-dark-font-style) !important;
    font-weight: var(--shiki-dark-font-weight) !important;
    text-decoration: var(--shiki-dark-text-decoration) !important;
    font-family: 'Maple-Mono', 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace !important;
    font-size: 16px !important;
  }

  /* Apply Light Theme (if multi-theme specified) */
  html[data-theme="light"] pre:has(code),
  html[data-theme="light"] pre:has(code) span {
    font-family: 'Maple-Mono', 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace !important;
    font-size: 16px !important;
  }

  /* Apply font to all code spans regardless of theme */
  pre code span {
    font-family: 'Maple-Mono', 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace !important;
    font-size: 16px !important;
  }
}
