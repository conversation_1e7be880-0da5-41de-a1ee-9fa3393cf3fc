<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="theme--agnostic" fill="none" width="1000" height="330">
		<style>
			.gauge-base {
				opacity: 0.1
			}

			.gauge-arc {
				fill: none;
				animation-delay: 250ms;
				stroke-linecap: round;
				transform: rotate(-90deg);
				transform-origin: 100px 60px;
				animation: load-gauge 1s ease forwards
			}

			.guage-text {
				font-size: 40px;
				font-family: monospace;
				text-align: center
			}

			.guage-red {
				color: #ff4e42;
				fill: #ff4e42;
				stroke: #ff4e42
			}
			.guage-orange {
				color: #ffa400;
				fill: #ffa400;
				stroke: #ffa400
			}
			.guage-green {
				color: #0cce6b;
				fill: #0cce6b;
				stroke: #0cce6b
			}
			.theme--agnostic .guage-undefined {
				color: #5c5c5c;
				fill: #5c5c5c;
				stroke: #5c5c5c
			}
			.theme--light .guage-undefined {
				color: #1e1e1e;
				fill: #1e1e1e;
				stroke: #1e1e1e
			}
			.theme--dark .guage-undefined {
				color: #f5f5f5;
				fill: #f5f5f5;
				stroke: #f5f5f5
			}

			.guage-title {
				stroke: none;
				font-size: 26px;
				line-height: 26px;
				font-family: Roboto, Halvetica, Arial, sans-serif
			}
			.metric.guage-title {
				font-family: 'Courier New', Courier, monospace
			}
			.theme--agnostic .guage-title {
				color: #737373;
				fill: #737373
			}
			.theme--light .guage-title {
				color: #212121;
				fill: #212121
			}
			.theme--dark .guage-title {
				color: #f5f5f5;
				fill: #f5f5f5
			}

			@keyframes load-gauge {
				from {
					stroke-dasharray: 0 352.858
				}
			}
			.lh-gauge--pwa__disc {
				fill: #e0e0e0
			}
			.lh-gauge--pwa__logo {
				position: relative;
				fill: #b0b0b0
			}
			.lh-gauge--pwa__invisible {
				display: none
			}
			.lh-gauge--pwa__visible {
				display: inline
			}
			.guage-invisible {
				display: none
			}
			.lh-gauge--pwa__logo--primary-color {
				fill: #304ffe
			}
			.theme--agnostic .lh-gauge--pwa__logo--secondary-color {
				fill: #787878
			}
			.theme--light .lh-gauge--pwa__logo--secondary-color {
				fill: #3d3d3d
			}
			.theme--dark .lh-gauge--pwa__logo--secondary-color {
				fill: #d8b6b6
			}
			.theme--light #svg_2 {
				stroke: #00000022
			}
			.theme--agnostic #svg_2 {
				stroke: #616161
			}
			.theme--light #svg_2 {
				stroke: #00000022
			}
			.theme--dark #svg_2 {
				stroke: #f5f5f566
			}
		</style>
		<svg class="guage-div guage-perf guage-green" viewBox="0 0 200 200" width="200" height="200" x="100" y="0">
		<circle class="gauge-base" r="56" cx="100" cy="60" stroke-width="8"/>
		<circle class="gauge-arc guage-arc-1" r="56" cx="100" cy="60" stroke-width="8" style="stroke-dasharray: 351.858, 351.858;"/>
		<text class="guage-text" x="100px" y="60px" alignment-baseline="central" dominant-baseline="central" text-anchor="middle">100</text>
		<text class="guage-title" x="100px" y="160px" alignment-baseline="central" dominant-baseline="central" text-anchor="middle">Performance</text>
	</svg>,<svg class="guage-div guage-perf guage-green" viewBox="0 0 200 200" width="200" height="200" x="300" y="0">
		<circle class="gauge-base" r="56" cx="100" cy="60" stroke-width="8"/>
		<circle class="gauge-arc guage-arc-1" r="56" cx="100" cy="60" stroke-width="8" style="stroke-dasharray: 351.858, 351.858;"/>
		<text class="guage-text" x="100px" y="60px" alignment-baseline="central" dominant-baseline="central" text-anchor="middle">100</text>
		<text class="guage-title" x="100px" y="160px" alignment-baseline="central" dominant-baseline="central" text-anchor="middle">Accessibility</text>
	</svg>,<svg class="guage-div guage-perf guage-green" viewBox="0 0 200 200" width="200" height="200" x="500" y="0">
		<circle class="gauge-base" r="56" cx="100" cy="60" stroke-width="8"/>
		<circle class="gauge-arc guage-arc-1" r="56" cx="100" cy="60" stroke-width="8" style="stroke-dasharray: 351.858, 351.858;"/>
		<text class="guage-text" x="100px" y="60px" alignment-baseline="central" dominant-baseline="central" text-anchor="middle">100</text>
		<text class="guage-title" x="100px" y="160px" alignment-baseline="central" dominant-baseline="central" text-anchor="middle">Best Practices</text>
	</svg>,<svg class="guage-div guage-perf guage-green" viewBox="0 0 200 200" width="200" height="200" x="700" y="0">
		<circle class="gauge-base" r="56" cx="100" cy="60" stroke-width="8"/>
		<circle class="gauge-arc guage-arc-1" r="56" cx="100" cy="60" stroke-width="8" style="stroke-dasharray: 351.858, 351.858;"/>
		<text class="guage-text" x="100px" y="60px" alignment-baseline="central" dominant-baseline="central" text-anchor="middle">100</text>
		<text class="guage-title" x="100px" y="160px" alignment-baseline="central" dominant-baseline="central" text-anchor="middle">SEO</text>
	</svg>
		<svg width="604" height="76" x="200" y="250">
			<g>
				<rect fill="none" id="canvas_background" height="80" width="604" y="-1" x="-1"/>
				<g display="none" overflow="visible" y="0" x="0" height="100%" width="100%" id="canvasGrid">
					<rect fill="url(#gridpattern)" stroke-width="0" y="0" x="0" height="100%" width="100%"/>
				</g>
			</g>
			<g>
				<rect fill-opacity="0" stroke-width="2" rx="40" id="svg_2" height="72" width="600" y="1" x="0" fill="#000000"/>
				<rect stroke="#000" rx="8" id="svg_3" height="14" width="48" y="30" x="35" stroke-opacity="null" stroke-width="0" fill="#ff4e42"/>
				<rect stroke="#000" rx="6" id="svg_4" height="14" width="48" y="30" x="220" stroke-opacity="null" stroke-width="0" fill="#ffa400"/>
				<rect stroke="#000" rx="6" id="svg_5" height="14" width="48" y="30" x="410" stroke-opacity="null" stroke-width="0" fill="#0cce6b"/>
				<text class="metric guage-title" xml:space="preserve" text-anchor="start" font-size="26" id="svg_6" y="45" x="100" stroke-opacity="null" stroke-width="0" stroke="#000">0-49</text>
				<text class="metric guage-title" xml:space="preserve" text-anchor="start" font-size="26" id="svg_7" y="45" x="280" stroke-opacity="null" stroke-width="0" stroke="#000">50-89</text>
				<text class="metric guage-title" xml:space="preserve" text-anchor="start" font-size="26" id="svg_8" y="45" x="470" stroke-opacity="null" stroke-width="0" stroke="#000">90-100</text>
			</g>
		</svg>
	</svg>