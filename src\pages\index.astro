---
import { getCollection } from "astro:content";
import Layout from "@/layouts/Layout.astro";
import Header from "@/components/Header.astro";
import Footer from "@/components/Footer.astro";
import Socials from "@/components/Socials.astro";
import LinkButton from "@/components/LinkButton.astro";
import Card from "@/components/Card.astro";
import Hr from "@/components/Hr.astro";
import getSortedPosts from "@/utils/getSortedPosts";
import IconRss from "@/assets/icons/IconRss.svg";
import IconArrowRight from "@/assets/icons/IconArrowRight.svg";
import { SITE } from "@/config";
import { SOCIALS } from "@/constants";

const posts = await getCollection("blog");

const sortedPosts = getSortedPosts(posts);
const featuredPosts = sortedPosts.filter(({ data }) => data.featured);
const recentPosts = sortedPosts.filter(({ data }) => !data.featured);
---

<Layout>
  <Header />
  <main id="main-content" data-layout="index">
    <section id="hero" class="pt-8 pb-6">
      <h1 class="my-4 inline-block text-4xl font-bold sm:my-8 sm:text-5xl">
        Hello
      </h1>
      <a
        target="_blank"
        href="/rss.xml"
        class="inline-block"
        aria-label="rss feed"
        title="RSS Feed"
      >
        <IconRss
          width={20}
          height={20}
          class="scale-125 stroke-accent stroke-3"
        />
        <span class="sr-only">RSS Feed</span>
      </a>

      <p>
        Welcome to my personal blog where I share my thoughts and findings on cybersecurity, web development, and technology.
      </p>
      <p class="mt-2">
        Read the blog posts or check
        <LinkButton
          class="underline decoration-dashed underline-offset-4 hover:text-accent"
          href="https://github.com/caelinya/astro-paper#readme"
        >
          README
        </LinkButton> for more info.
      </p>
      {
        // only display if at least one social link is enabled
        SOCIALS.length > 0 && (
          <div class="mt-4 flex flex-col sm:flex-row sm:items-center">
            <div class="mr-2 mb-1 whitespace-nowrap sm:mb-0">Social Links:</div>
            <Socials />
          </div>
        )
      }
    </section>

    <Hr />

    {
      featuredPosts.length > 0 && (
        <>
          <section id="featured" class="pt-12 pb-6">
            <h2 class="text-2xl font-semibold tracking-wide">Featured</h2>
            <ul>
              {featuredPosts.map(data => (
                <Card variant="h3" {...data} />
              ))}
            </ul>
          </section>
          {recentPosts.length > 0 && <Hr />}
        </>
      )
    }

    {
      recentPosts.length > 0 && (
        <section id="recent-posts" class="pt-12 pb-6">
          <h2 class="text-2xl font-semibold tracking-wide">Recent Posts</h2>
          <ul>
            {recentPosts.map(
              (data, index) =>
                index < SITE.postPerIndex && <Card variant="h3" {...data} />
            )}
          </ul>
        </section>
      )
    }

    <div class="my-8 text-center">
      <LinkButton href="/posts/">
        All Posts
        <IconArrowRight class="inline-block" />
      </LinkButton>
    </div>
  </main>
  <Footer />
</Layout>

<script>
  document.addEventListener("astro:page-load", () => {
    const indexLayout = (document.querySelector("#main-content") as HTMLElement)
      ?.dataset?.layout;
    if (indexLayout) {
      sessionStorage.setItem("backUrl", "/");
    }
  });
</script>
